#ifndef _POSTPROCESS_H_
#define _POSTPROCESS_H_

#include <stdint.h>
#include <vector>
#include "rknn_api.h"
#include "common.h"
#include "image_utils.h"

#define OBJ_NAME_MAX_SIZE 64
//OBJ_NUMB_MAX_SIZE: 表示目标检测结果中允许的最大目标数量。
//OBJ_CLASS_NUM: 表示目标检测模型支持的类别数量。这里设置为 1，说明模型只支持一个类别的目标检测。
//NMS_THRESH: 非极大值抑制（NMS）的阈值，用于过滤重叠的检测框。值为 0.4，表示当两个检测框的重叠程度（IoU）超过 0.4 时，保留置信度更高的框，抑制其他框。
//BOX_THRESH: 检测框的置信度阈值。值为 0.5，表示只有置信度大于 0.5 的检测框才会被认为是有效目标并参与后续处理。
#define OBJ_NUMB_MAX_SIZE 6
#define OBJ_CLASS_NUM 1
#define NMS_THRESH 0.4
#define BOX_THRESH 0.6
#define PROP_BOX_SIZE (5 + OBJ_CLASS_NUM)

// class rknn_app_context_t;

typedef struct {
    image_rect_t box;
    float keypoints[17][3];//keypoints x,y,conf
    float prop;
    int cls_id;
} object_detect_result;

typedef struct {
    int id;
    int count;
    object_detect_result results[OBJ_NUMB_MAX_SIZE];
} object_detect_result_list;

int init_post_process();
void deinit_post_process();
char *coco_cls_to_name(int cls_id);
int post_process(rknn_app_context_t *app_ctx, void *outputs, letterbox_t *letter_box, float conf_threshold, float nms_threshold, object_detect_result_list *od_results);

void deinitPostProcess();
#endif //_RKNN_YOLOV5_DEMO_POSTPROCESS_H_

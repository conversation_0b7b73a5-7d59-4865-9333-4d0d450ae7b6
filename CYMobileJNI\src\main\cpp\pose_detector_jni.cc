#include <jni.h>
#include <android/bitmap.h>
#include <android/log.h>
#include <string>
#include <vector>
#include <mutex>
#include <arm_neon.h>  // 添加NEON指令集头文件

#include "yolov8-pose.h"


#define TAG "PoseDetectorJNI"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__)

// 全局上下文
static rknn_app_context_t g_app_ctx;
static bool g_is_initialized = false;
static std::mutex g_mutex; // 添加互斥锁保证线程安全

// NEON优化的图像旋转180度函数 - 添加在extern "C" {之前
// 在rotate_image_180_neon函数中添加内存对齐和边界检查
void rotate_image_180_neon(uint8_t *src, uint8_t *dst, int width, int height) {
    const int pixelCount = width * height;
    const int step = 4; // RGBA每像素4字节
    const int vectorSize = 16; // 一次处理16字节(4个像素)

    // 检查内存对齐
    uintptr_t srcAddr = reinterpret_cast<uintptr_t>(src);
    uintptr_t dstAddr = reinterpret_cast<uintptr_t>(dst);
    bool isAligned = ((srcAddr % 16) == 0) && ((dstAddr % 16) == 0);

    int i = 0;
    if (isAligned) {
        // 对齐版本 - 使用NEON指令
        for (; i <= pixelCount - vectorSize / step; i += vectorSize / step) {
            // 计算源和目标索引
            int srcIdx = (pixelCount - i - vectorSize / step) * step;
            int dstIdx = i * step;

            // 加载4个像素(16字节)到NEON寄存器
            uint8x16_t pixels = vld1q_u8(&src[srcIdx]);

            // 存储到目标位置
            vst1q_u8(&dst[dstIdx], pixels);
        }
    }

    // 处理剩余像素或非对齐情况
    for (; i < pixelCount; i++) {
        int srcIdx = (pixelCount - i - 1) * step;
        int dstIdx = i * step;

        dst[dstIdx] = src[srcIdx];         // R
        dst[dstIdx + 1] = src[srcIdx + 1]; // G
        dst[dstIdx + 2] = src[srcIdx + 2]; // B
        dst[dstIdx + 3] = src[srcIdx + 3]; // A
    }
}

// 标准C实现的图像旋转180度函数 - 用于不支持NEON的设备
void rotate_image_180_standard(uint8_t *src, uint8_t *dst, int width, int height) {
    const int pixelCount = width * height;
    const int step = 4; // RGBA每像素4字节

    for (int i = 0; i < pixelCount; i++) {
        int srcIdx = (pixelCount - i - 1) * step;
        int dstIdx = i * step;

        dst[dstIdx] = src[srcIdx];         // R
        dst[dstIdx + 1] = src[srcIdx + 1]; // G
        dst[dstIdx + 2] = src[srcIdx + 2]; // B
        dst[dstIdx + 3] = src[srcIdx + 3]; // A
    }
}


extern "C" {
JNIEXPORT jobject JNICALL
Java_com_cykj_cymobile_PoseDetector_getBitmap(
        JNIEnv *env, jobject obj, jlong path, jint width, jint height, jobject bitmap) {
    // 获取Android Bitmap相关类
    jclass bitmapConfigClass = env->FindClass("android/graphics/Bitmap$Config");
    jfieldID argb8888FieldID = env->GetStaticFieldID(bitmapConfigClass, "ARGB_8888",
                                                     "Landroid/graphics/Bitmap$Config;");
    jobject argb8888Obj = env->GetStaticObjectField(bitmapConfigClass, argb8888FieldID);
    jclass bitmapClass = env->FindClass("android/graphics/Bitmap");
    jobject bitmapObj = bitmap;
    bool needNewBitmap = true;
    // 检查传入的bitmap是否可以复用
    if (bitmap != NULL) {
        // 获取Bitmap的宽高
        jmethodID getWidthMethodID = env->GetMethodID(bitmapClass, "getWidth", "()I");
        jmethodID getHeightMethodID = env->GetMethodID(bitmapClass, "getHeight", "()I");
        jint bitmapWidth = env->CallIntMethod(bitmap, getWidthMethodID);
        jint bitmapHeight = env->CallIntMethod(bitmap, getHeightMethodID);
        // 检查Bitmap的配置
        jmethodID getConfigMethodID = env->GetMethodID(bitmapClass, "getConfig", "()Landroid/graphics/Bitmap$Config;");
        jobject bitmapConfig = env->CallObjectMethod(bitmap, getConfigMethodID);
        // 如果尺寸和配置都匹配，则可以复用
        if (bitmapWidth == width && bitmapHeight == height &&
            env->IsSameObject(bitmapConfig, argb8888Obj)) {
            needNewBitmap = false;
            bitmapObj = bitmap;
        }
        if (bitmapConfig != NULL) {
            env->DeleteLocalRef(bitmapConfig);
        }
    }
    // 如果不能复用，创建新的Bitmap
    if (needNewBitmap) {
        jmethodID createBitmapMethodID = env->GetStaticMethodID(bitmapClass, "createBitmap",
                                                                "(IILandroid/graphics/Bitmap$Config;)Landroid/graphics/Bitmap;");
        bitmapObj = env->CallStaticObjectMethod(bitmapClass, createBitmapMethodID, width,
                                                height, argb8888Obj);
    }
    // 获取setPixels方法
    jmethodID setPixelsMethodID = env->GetMethodID(bitmapClass, "setPixels", "([IIIIIII)V");
    // 创建像素数组
    jintArray pixelArray = env->NewIntArray(width * height);
    jint *pixels = env->GetIntArrayElements(pixelArray, NULL);
    // 获取RGBA数据
    uint8_t *rgbaData = reinterpret_cast<uint8_t *>(path);
    // 转换RGBA到ARGB
    // 转换RGBA到ARGB并旋转180度 (通过倒序写入实现)
    for (int i = 0; i < width * height; i++) {
        int srcIndex = (width * height - 1 - i);  // 倒序索引
        int r = rgbaData[srcIndex * 4] & 0xFF;
        int g = rgbaData[srcIndex * 4 + 1] & 0xFF;
        int b = rgbaData[srcIndex * 4 + 2] & 0xFF;
        int a = rgbaData[srcIndex * 4 + 3] & 0xFF;
        // ARGB格式
        pixels[i] = (a << 24) | (r << 16) | (g << 8) | b;
    }
    // 更新像素数组
    env->ReleaseIntArrayElements(pixelArray, pixels, 0);
    // 设置Bitmap像素
    env->CallVoidMethod(bitmapObj, setPixelsMethodID, pixelArray, 0, width, 0, 0, width, height);
    // 清理
    env->DeleteLocalRef(pixelArray);
    env->DeleteLocalRef(bitmapConfigClass);
    env->DeleteLocalRef(argb8888Obj);
    env->DeleteLocalRef(bitmapClass);

    // 如果创建了新的Bitmap，返回它；否则返回传入的Bitmap
    return bitmapObj;
}

JNIEXPORT void JNICALL
Java_com_cykj_cymobile_PoseDetector_longToByteArray(JNIEnv *env, jobject obj, jlong path,jbyteArray array, jint size) {
    jbyte *src = reinterpret_cast<jbyte *>(path);
    env->SetByteArrayRegion(array, 0, size, src);
}

// 初始化模型
JNIEXPORT jint JNICALL Java_com_cykj_cymobile_PoseDetector_init(
        JNIEnv* env, jobject thiz, jstring model_path) {
    std::lock_guard<std::mutex> lock(g_mutex);

    if (g_is_initialized) {
        LOGD("Model already initialized");
        return 0;
    }

    const char* model_path_ptr = env->GetStringUTFChars(model_path, nullptr);
    if (!model_path_ptr) {
//        LOGE("Failed to get model path string");
        return -1;
    }

    LOGD("Model path: %s", model_path_ptr);

    // 初始化模型
    int ret = init_yolov8_pose_model(model_path_ptr, &g_app_ctx);

    env->ReleaseStringUTFChars(model_path, model_path_ptr);

    // 初始化后处理
    if (ret == 0) {
        ret = init_post_process();
        if (ret == 0) {
            g_is_initialized = true;
            LOGD("Model initialized successfully");
        } else {
//            LOGE("Failed to initialize post process, ret=%d", ret);
            // 清理已初始化的资源
            release_yolov8_pose_model(&g_app_ctx);
        }
    } else {
//        LOGE("Failed to initialize model, ret=%d", ret);
    }

    return ret;
}

// 将检测结果转换为Java对象数组
jobjectArray convert_results_to_java(JNIEnv* env, object_detect_result_list* results) {
    // 检查结果是否为空
    if (!results || results->count <= 0) {
        jclass pose_result_class = env->FindClass("com/cykj/cymobile/PoseResult");
        if (pose_result_class == NULL) {
//            LOGE("Failed to find PoseResult class");
            return NULL;
        }
        return env->NewObjectArray(0, pose_result_class, NULL);
    }

    // 获取Java类
    jclass pose_result_class = env->FindClass("com/cykj/cymobile/PoseResult");
    if (pose_result_class == NULL) {
//        LOGE("Failed to find PoseResult class");
        return NULL;
    }

    // 获取构造函数ID
    jmethodID constructor = env->GetMethodID(pose_result_class, "<init>", "()V");
    if (constructor == NULL) {
//        LOGE("Failed to find PoseResult constructor");
        return NULL;
    }

    // 获取字段ID
    jfieldID box_left_id = env->GetFieldID(pose_result_class, "boxLeft", "I");
    jfieldID box_top_id = env->GetFieldID(pose_result_class, "boxTop", "I");
    jfieldID box_right_id = env->GetFieldID(pose_result_class, "boxRight", "I");
    jfieldID box_bottom_id = env->GetFieldID(pose_result_class, "boxBottom", "I");
    jfieldID confidence_id = env->GetFieldID(pose_result_class, "confidence", "F");
    jfieldID class_id_id = env->GetFieldID(pose_result_class, "classId", "I");
    jfieldID keypoints_id = env->GetFieldID(pose_result_class, "keypoints", "[[F");

    // 检查所有字段ID是否获取成功
    if (!box_left_id || !box_top_id || !box_right_id || !box_bottom_id ||
        !confidence_id || !class_id_id || !keypoints_id) {
//        LOGE("Failed to get field IDs");
        return NULL;
    }

    // 创建结果数组
    jobjectArray result_array = env->NewObjectArray(results->count, pose_result_class, NULL);
    if (result_array == NULL) {
//        LOGE("Failed to create result array");
        return NULL;
    }

    // 填充结果
    for (int i = 0; i < results->count; i++) {
        // 创建PoseResult对象
        jobject pose_result = env->NewObject(pose_result_class, constructor);
        if (pose_result == NULL) {
//            LOGE("Failed to create PoseResult object");
            return result_array; // 返回已创建的部分结果
        }

        // 设置边界框
        env->SetIntField(pose_result, box_left_id, results->results[i].box.left);
        env->SetIntField(pose_result, box_top_id, results->results[i].box.top);
        env->SetIntField(pose_result, box_right_id, results->results[i].box.right);
        env->SetIntField(pose_result, box_bottom_id, results->results[i].box.bottom);

        // 设置置信度和类别ID
        env->SetFloatField(pose_result, confidence_id, results->results[i].prop);
        env->SetIntField(pose_result, class_id_id, results->results[i].cls_id);

        // 创建关键点数组
        jclass float_array_class = env->FindClass("[F");
        if (float_array_class == NULL) {
//            LOGE("Failed to find float array class");
            env->DeleteLocalRef(pose_result);
            continue;
        }

        jobjectArray keypoints_array = env->NewObjectArray(17, float_array_class, NULL);
        if (keypoints_array == NULL) {
//            LOGE("Failed to create keypoints array");
            env->DeleteLocalRef(pose_result);
            env->DeleteLocalRef(float_array_class);
            continue;
        }

        // 填充关键点
        for (int j = 0; j < 17; j++) {
            jfloatArray point = env->NewFloatArray(3);
            if (point == NULL) {
//                LOGE("Failed to create point array");
                continue;
            }

            jfloat point_data[3] = {
                    results->results[i].keypoints[j][0],
                    results->results[i].keypoints[j][1],
                    results->results[i].keypoints[j][2]
            };
            env->SetFloatArrayRegion(point, 0, 3, point_data);
            env->SetObjectArrayElement(keypoints_array, j, point);
            env->DeleteLocalRef(point);
        }

        // 设置关键点数组
        env->SetObjectField(pose_result, keypoints_id, keypoints_array);
        env->DeleteLocalRef(keypoints_array);

        // 添加到结果数组
        env->SetObjectArrayElement(result_array, i, pose_result);
        env->DeleteLocalRef(pose_result);
    }

    return result_array;
}

// 检测图像中的人体姿态
JNIEXPORT jobjectArray JNICALL Java_com_cykj_cymobile_PoseDetector_detect(
        JNIEnv* env, jobject thiz, jobject bitmap) {
    std::lock_guard<std::mutex> lock(g_mutex);

    if (!g_is_initialized) {
//        LOGE("Model not initialized");
        return NULL;
    }

    AndroidBitmapInfo info;
    void* pixels;
    int ret;

    // 获取Bitmap信息
    if ((ret = AndroidBitmap_getInfo(env, bitmap, &info)) < 0) {
//        LOGE("AndroidBitmap_getInfo() failed! error=%d", ret);
        return NULL;
    }

    // 锁定Bitmap像素
    if ((ret = AndroidBitmap_lockPixels(env, bitmap, &pixels)) < 0) {
//        LOGE("AndroidBitmap_lockPixels() failed! error=%d", ret);
        return NULL;
    }

    // 创建图像缓冲区
    image_buffer_t img_buffer;
    unsigned char* converted_buffer = nullptr;

    // 根据Bitmap格式设置对应的image_format_t
    if (info.format == ANDROID_BITMAP_FORMAT_RGBA_8888) {
        img_buffer.virt_addr = (unsigned char*)pixels;
        img_buffer.format = IMAGE_FORMAT_RGBA8888;
        img_buffer.width = info.width;
        img_buffer.height = info.height;
        img_buffer.width_stride = info.stride;
        img_buffer.height_stride = info.height;
        img_buffer.size = info.height * info.stride;
    } else if (info.format == ANDROID_BITMAP_FORMAT_RGB_565) {
        // 转换RGB565到RGB888
        converted_buffer = (unsigned char*)malloc(info.width * info.height * 3);
        if (!converted_buffer) {
//            LOGE("Failed to allocate memory for RGB888 buffer");
            AndroidBitmap_unlockPixels(env, bitmap);
            return NULL;
        }

        uint16_t* rgb565_data = (uint16_t*)pixels;
        for (int i = 0; i < info.height; i++) {
            for (int j = 0; j < info.width; j++) {
                uint16_t pixel = rgb565_data[i * (info.stride / 2) + j];
                int index = (i * info.width + j) * 3;

                // 从RGB565提取RGB分量
                converted_buffer[index] = ((pixel >> 11) & 0x1F) << 3;  // R
                converted_buffer[index + 1] = ((pixel >> 5) & 0x3F) << 2;  // G
                converted_buffer[index + 2] = (pixel & 0x1F) << 3;  // B
            }
        }

        img_buffer.virt_addr = converted_buffer;
        img_buffer.format = IMAGE_FORMAT_RGB888;
        img_buffer.width = info.width;
        img_buffer.height = info.height;
        img_buffer.width_stride = info.width * 3;
        img_buffer.height_stride = info.height;
        img_buffer.size = info.width * info.height * 3;
    } else {
//        LOGE("不支持的Bitmap格式: %d", info.format);
        AndroidBitmap_unlockPixels(env, bitmap);
        return NULL;
    }

    img_buffer.fd = -1;  // 通常不使用文件描述符

    // 处理图像并检测 - 使用阈值参数
    object_detect_result_list detect_result_list;

    // 如果inference_yolov8_pose_model支持阈值参数，可以修改为以下调用
    // ret = inference_yolov8_pose_model_with_thresholds(&g_app_ctx, &img_buffer,
    //                                                 &detect_result_list,
    //                                                 threshold, nms_threshold);

    // 当前使用原有函数
    ret = inference_yolov8_pose_model(&g_app_ctx, &img_buffer, &detect_result_list);

    // 解锁Bitmap像素
    AndroidBitmap_unlockPixels(env, bitmap);

    // 释放转换缓冲区（如果有）
    if (converted_buffer) {
        free(converted_buffer);
    }

    if (ret != 0) {
//        LOGE("inference_yolov8_pose_model failed! ret=%d", ret);
        return NULL;
    }

    // 将检测结果转换为Java对象数组
    jobjectArray result_array = convert_results_to_java(env, &detect_result_list);

    // 如果detect_result_list需要手动释放，在这里添加释放代码
    // free_detect_result_list(&detect_result_list);

    return result_array;
}

// 释放资源
JNIEXPORT jint JNICALL Java_com_cykj_cymobile_PoseDetector_release(
        JNIEnv* env, jobject thiz) {
    std::lock_guard<std::mutex> lock(g_mutex);
    if (!g_is_initialized) {
//        LOGD("Model not initialized or already released");
        return 0;
    }
    // 释放资源
    deinit_post_process();
    int ret = release_yolov8_pose_model(&g_app_ctx);
    // 重置初始化标志
    g_is_initialized = false;
//    LOGD("Model released, ret=%d", ret);
    return ret;
}
// 直接从字节数组检测姿态 - 使用NEON优化
JNIEXPORT jobjectArray JNICALL Java_com_cykj_cymobile_PoseDetector_detectFromByteArray(
        JNIEnv* env, jobject thiz, jbyteArray imageData, jint width, jint height) {
    std::lock_guard<std::mutex> lock(g_mutex);

    if (!g_is_initialized) {
//        LOGE("Model not initialized");
        return NULL;
    }

    // 增强参数验证
    if (imageData == NULL) {
//        LOGE("Invalid parameter: imageData is NULL");
        return NULL;
    }

    // 检查图像尺寸
    if (width <= 0 || height <= 0 || width > 4096 || height > 4096) {
//        LOGE("Invalid image dimensions: width=%d, height=%d", width, height);
        return NULL;
    }

    // 检查数组长度
    jsize arrayLength = env->GetArrayLength(imageData);
    const int expectedSize = width * height * 4; // RGBA格式，每像素4字节
    if (arrayLength < expectedSize) {
//        LOGE("Image data array too small: expected=%d, actual=%d", expectedSize, arrayLength);
        return NULL;
    }

    // 获取字节数组元素
    jbyte* bytes = env->GetByteArrayElements(imageData, NULL);
    if (bytes == NULL) {
//        LOGE("Failed to get byte array elements");
        return NULL;
    }

    // 检查数据有效性（简单检查非全零）
    bool hasValidData = false;
    for (int i = 0; i < std::min(arrayLength, 1024); i++) {
        if (bytes[i] != 0) {
            hasValidData = true;
            break;
        }
    }

    if (!hasValidData) {
//        LOGW("Image data appears to be empty or invalid (all zeros in sample)");
        // 继续处理，因为全零数据也可能是有效的，只是记录警告
    }

    // 创建图像缓冲区
    image_buffer_t img_buffer;
    unsigned char* rotated_buffer = nullptr;

    try {
        // 分配旋转后的缓冲区
        const int totalPixels = width * height;
        const int bufferSize = totalPixels * 4; // RGBA格式，每像素4字节
        rotated_buffer = (unsigned char*)malloc(bufferSize);

        if (!rotated_buffer) {
//            LOGE("Failed to allocate memory for rotated buffer");
            env->ReleaseByteArrayElements(imageData, bytes, JNI_ABORT);
            return NULL;
        }

        // 安全地旋转图像
        try {
            // 使用NEON优化的图像旋转180度
            rotate_image_180_neon((uint8_t*)bytes, rotated_buffer, width, height);
        } catch (...) {
//            LOGE("Exception during image rotation, falling back to standard rotation");
            // 如果NEON优化版本失败，回退到标准版本
            rotate_image_180_standard((uint8_t*)bytes, rotated_buffer, width, height);
        }

        // 设置图像缓冲区
        img_buffer.virt_addr = rotated_buffer;
        img_buffer.format = IMAGE_FORMAT_RGBA8888;
        img_buffer.width = width;
        img_buffer.height = height;
        img_buffer.width_stride = width * 4;
        img_buffer.height_stride = height;
        img_buffer.size = bufferSize;
        img_buffer.fd = -1;  // 不使用文件描述符

        // 处理图像并检测
        object_detect_result_list detect_result_list;
        int ret = inference_yolov8_pose_model(&g_app_ctx, &img_buffer, &detect_result_list);

        // 释放字节数组
        env->ReleaseByteArrayElements(imageData, bytes, JNI_ABORT);

        if (ret != 0) {
//            LOGE("inference_yolov8_pose_model failed! ret=%d", ret);
            free(rotated_buffer);
            return NULL;
        }

        // 将检测结果转换为Java对象数组
        jobjectArray result_array = convert_results_to_java(env, &detect_result_list);

        // 释放旋转缓冲区
        free(rotated_buffer);

        return result_array;
    } catch (const std::exception& e) {
//        LOGE("Exception during image processing: %s", e.what());
        if (rotated_buffer) free(rotated_buffer);
        env->ReleaseByteArrayElements(imageData, bytes, JNI_ABORT);
        return NULL;
    } catch (...) {
//        LOGE("Unknown exception during image processing");
        if (rotated_buffer) free(rotated_buffer);
        env->ReleaseByteArrayElements(imageData, bytes, JNI_ABORT);
        return NULL;
    }
}


} // extern "C"
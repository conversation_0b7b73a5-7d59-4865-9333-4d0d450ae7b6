// Copyright (c) 2024 by Rockchip Electronics Co., Ltd. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

#include "yolov8-pose.h"
#include "common.h"

#include "image_utils.h"

#include <sys/time.h>
#include <android/log.h> // 添加头文件

#define TAG "YOLOv8Pose" // 定义日志标识符
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, TAG, __VA_ARGS__)

static inline int64_t getCurrentTimeUs()
{
  struct timeval tv;
  gettimeofday(&tv, NULL);
  return tv.tv_sec * 1000000 + tv.tv_usec;
}

static void dump_tensor_attr(rknn_tensor_attr *attr)
{
    LOGD("  index=%d, name=%s, n_dims=%d, dims=[%d, %d, %d, %d], n_elems=%d, size=%d, fmt=%s, type=%s, qnt_type=%s, "
           "zp=%d, scale=%f\n",
           attr->index, attr->name, attr->n_dims, attr->dims[0], attr->dims[1], attr->dims[2], attr->dims[3],
           attr->n_elems, attr->size, get_format_string(attr->fmt), get_type_string(attr->type),
           get_qnt_type_string(attr->qnt_type), attr->zp, attr->scale);
}

/**
 * - 通过 rknn_query 函数查询模型的输入张量属性
- 根据模型的数据格式(NCHW或NHWC)从 dims 数组中提取宽度、高度和通道数
- 将这些值存储在 app_ctx 结构体中，供后续使用
 整个推理流程如下：
1. 初始化阶段 ：
   - 加载模型文件
   - 查询模型输入输出信息
   - 从模型中获取输入尺寸(640x640)和格式要求
2. 推理阶段 ：
   - 创建一个与模型输入尺寸相匹配的目标图像缓冲区(640x640, RGB888)
   - 使用 convert_image_with_letterbox 函数将输入图像缩放到模型所需尺寸
   - 设置模型输入并执行推理
   - 获取模型输出并进行后处理
## 为什么使用640x640和RGB888格式？
1. 尺寸选择 ：
   - YOLOv8-Pose模型通常在640x640的输入尺寸上训练
   - 这个尺寸在精度和速度之间取得了良好的平衡
2. 格式选择 ：
   - RGB888是深度学习模型常用的输入格式
   - 每个像素3个字节，分别表示红、绿、蓝通道
   - 相比RGBA8888少了Alpha通道，减少了内存占用和计算量
 */
int init_yolov8_pose_model(const char *model_path, rknn_app_context_t *app_ctx)
{
    int ret;
    rknn_context ctx = 0;

    ret = rknn_init(&ctx, (char *)model_path, 0, 0, NULL);
    if (ret < 0)
    {
//        LOGD("rknn_init fail! ret=%d\n", ret);
        return -1;
    }

    // Get Model Input Output Number
    rknn_input_output_num io_num;
    ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
    if (ret != RKNN_SUCC)
    {
//        LOGD("rknn_query fail! ret=%d\n", ret);
        return -1;
    }
//    LOGD("model input num: %d, output num: %d\n", io_num.n_input, io_num.n_output);

    // Get Model Input Info
//    LOGD("input tensors:\n");
    // 查询模型输入属性
    rknn_tensor_attr input_attrs[io_num.n_input];
    memset(input_attrs, 0, sizeof(input_attrs));
    for (int i = 0; i < io_num.n_input; i++)
    {
        input_attrs[i].index = i;
        ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &(input_attrs[i]), sizeof(rknn_tensor_attr));
        if (ret != RKNN_SUCC)
        {
//            LOGD("rknn_query fail! ret=%d\n", ret);
            return -1;
        }
        dump_tensor_attr(&(input_attrs[i]));
    }

    // Get Model Output Info
//    LOGD("output tensors:\n");
    rknn_tensor_attr output_attrs[io_num.n_output];
    memset(output_attrs, 0, sizeof(output_attrs));
    for (int i = 0; i < io_num.n_output; i++)
    {
        output_attrs[i].index = i;
        ret = rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs[i]), sizeof(rknn_tensor_attr));
        if (ret != RKNN_SUCC)
        {
//            LOGD("rknn_query fail! ret=%d\n", ret);
            return -1;
        }
        dump_tensor_attr(&(output_attrs[i]));
    }

    // Set to context
    app_ctx->rknn_ctx = ctx;

    // TODO
    if (output_attrs[0].qnt_type == RKNN_TENSOR_QNT_AFFINE_ASYMMETRIC && output_attrs[0].type != RKNN_TENSOR_FLOAT16)
    {
        app_ctx->is_quant = true;
    }
    else
    {
        app_ctx->is_quant = false;
    }

    app_ctx->io_num = io_num;
    app_ctx->input_attrs = (rknn_tensor_attr *)malloc(io_num.n_input * sizeof(rknn_tensor_attr));
    memcpy(app_ctx->input_attrs, input_attrs, io_num.n_input * sizeof(rknn_tensor_attr));
    app_ctx->output_attrs = (rknn_tensor_attr *)malloc(io_num.n_output * sizeof(rknn_tensor_attr));
    memcpy(app_ctx->output_attrs, output_attrs, io_num.n_output * sizeof(rknn_tensor_attr));
// 根据模型输入格式设置宽高和通道数
    if (input_attrs[0].fmt == RKNN_TENSOR_NCHW)
    {
//        LOGD("model is NCHW input fmt\n");
        app_ctx->model_channel = input_attrs[0].dims[1];
        app_ctx->model_height = input_attrs[0].dims[2];
        app_ctx->model_width = input_attrs[0].dims[3];
    }
    else
    {
//        LOGD("model is NHWC input fmt\n");
        app_ctx->model_height = input_attrs[0].dims[1];
        app_ctx->model_width = input_attrs[0].dims[2];
        app_ctx->model_channel = input_attrs[0].dims[3];
    }
//    LOGD("model input height=%d, width=%d, channel=%d\n",
//           app_ctx->model_height, app_ctx->model_width, app_ctx->model_channel);

    return 0;
}

int release_yolov8_pose_model(rknn_app_context_t *app_ctx)
{
    if (app_ctx->input_attrs != NULL)
    {
        free(app_ctx->input_attrs);
        app_ctx->input_attrs = NULL;
    }
    if (app_ctx->output_attrs != NULL)
    {
        free(app_ctx->output_attrs);
        app_ctx->output_attrs = NULL;
    }
    if (app_ctx->rknn_ctx != 0)
    {
        rknn_destroy(app_ctx->rknn_ctx);
        app_ctx->rknn_ctx = 0;
    }
    return 0;
}


int inference_yolov8_pose_model(rknn_app_context_t *app_ctx, image_buffer_t *img, object_detect_result_list *od_results)
{
    int ret;
    image_buffer_t dst_img;
    letterbox_t letter_box;
    rknn_input inputs[app_ctx->io_num.n_input];
    rknn_output outputs[app_ctx->io_num.n_output];
    const float nms_threshold = NMS_THRESH;      // Default NMS threshold
    const float box_conf_threshold = BOX_THRESH; // Default box threshold
    int bg_color = 114;

    if ((!app_ctx) || !(img) || (!od_results))
    {
        return -1;
    }

    memset(od_results, 0x00, sizeof(*od_results));
    memset(&letter_box, 0, sizeof(letterbox_t));
    memset(&dst_img, 0, sizeof(image_buffer_t));
    memset(inputs, 0, sizeof(inputs));
    memset(outputs, 0, sizeof(outputs));
// 添加输入图像信息的日志
//    LOGD("Input image info: width=%d, height=%d, format=%d, size=%d, virt_addr=%p",
//         img->width, img->height, img->format, img->size, img->virt_addr);
    // Pre Process
    //这里创建了一个新的图像缓冲区，尺寸为模型所需的输入尺寸。
    //然后通过 convert_image_with_letterbox 函数进行缩放:
    dst_img.width = app_ctx->model_width;
    dst_img.height = app_ctx->model_height;
    dst_img.format = IMAGE_FORMAT_RGB888;
    dst_img.size = get_image_size(&dst_img);
    dst_img.virt_addr = (unsigned char *)malloc(dst_img.size);
    // 添加目标图像信息的日志
//    LOGD("Destination image info: width=%d, height=%d, format=%d, size=%d, virt_addr=%p",
//         dst_img.width, dst_img.height, dst_img.format, dst_img.size, dst_img.virt_addr);
    if (dst_img.virt_addr == NULL)
    {
//        LOGD("malloc buffer size:%d fail!\n", dst_img.size);
        goto out;
    }
// letterbox
//    LOGD("Before convert_image_with_letterbox: src format=%d, dst format=%d, bg_color=%d",
//         img->format, dst_img.format, bg_color);
    // letterbox
    ret = convert_image_with_letterbox(img, &dst_img, &letter_box, bg_color);
    if (ret < 0)
    {
//        LOGD("convert_image_with_letterbox fail! ret=%d, src format=%d, dst format=%d",
//             ret, img->format, dst_img.format);
        // 检查图像数据是否有效
        if (img->virt_addr == NULL) {
//            LOGD("Source image virtual address is NULL!");
        }
        if (img->width <= 0 || img->height <= 0) {
//            LOGD("Invalid source image dimensions: %dx%d", img->width, img->height);
        }
        goto out;
    }
    // Set Input Data
    inputs[0].index = 0;
    inputs[0].type = RKNN_TENSOR_UINT8;
    inputs[0].fmt = RKNN_TENSOR_NHWC;
    inputs[0].size = app_ctx->model_width * app_ctx->model_height * app_ctx->model_channel;
    inputs[0].buf = dst_img.virt_addr;

    ret = rknn_inputs_set(app_ctx->rknn_ctx, app_ctx->io_num.n_input, inputs);
    if (ret < 0)
    {
//        LOGD("rknn_input_set fail! ret=%d\n", ret);
        goto out;
    }

    // Run
//    LOGD("rknn_run\n");
    int start_us,end_us;
    start_us = getCurrentTimeUs();
    ret = rknn_run(app_ctx->rknn_ctx, nullptr);
    end_us = getCurrentTimeUs() - start_us;
//    LOGD("rknn_run time=%.2fms, FPS = %.2f\n",end_us / 1000.f,
//            1000.f * 1000.f / end_us);

    if (ret < 0)
    {
//        LOGD("rknn_run fail! ret=%d\n", ret);
        goto out;
    }

    // Get Output
    memset(outputs, 0, sizeof(outputs));
    for (int i = 0; i < app_ctx->io_num.n_output; i++)
    {
        outputs[i].index = i;
        outputs[i].want_float = (!app_ctx->is_quant);
    }
    ret = rknn_outputs_get(app_ctx->rknn_ctx, app_ctx->io_num.n_output, outputs, NULL);
    if (ret < 0)
    {
//        LOGD("rknn_outputs_get fail! ret=%d\n", ret);
        goto out;
    }
    // Post Process
    start_us = getCurrentTimeUs();
    post_process(app_ctx, outputs, &letter_box, box_conf_threshold, nms_threshold, od_results);
    end_us = getCurrentTimeUs() - start_us;
//    printf("post_process time=%.2fms, FPS = %.2f\n",end_us / 1000.f,
//            1000.f * 1000.f / end_us);
//    LOGD("post_process time=%.2fms, FPS = %.2f", end_us / 1000.f, 1000.f * 1000.f / end_us);
    // Remeber to release rknn output
    rknn_outputs_release(app_ctx->rknn_ctx, app_ctx->io_num.n_output, outputs);

out:
    if (dst_img.virt_addr != NULL)
    {
        free(dst_img.virt_addr);
    }

    return ret;
}
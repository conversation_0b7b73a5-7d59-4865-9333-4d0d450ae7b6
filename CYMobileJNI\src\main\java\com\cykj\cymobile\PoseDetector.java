package com.cykj.cymobile;

import android.graphics.Bitmap;



public class PoseDetector {
    static {
        System.loadLibrary("rknnrt");
        System.loadLibrary("rknn_pose_detector");
    }

    /**
     * 初始化模型
     * @param modelPath 模型文件路径
     * @return 0表示成功，其他值表示失败
     */
    public native int init(String modelPath);

    /**
     * 检测图像中的人体姿态
     * @param bitmap 输入图像
     * @return 检测结果数组
     */
    public native PoseResult[] detect(Bitmap bitmap);

    /**
     * 释放资源
     * @return 0表示成功，其他值表示失败
     */
    public native int release();

    /**
     * 从内存地址获取帧数据
     * @param var0 内存地址
     * @param var2 帧数据
     * @param var3  size = imageWidth * imageHeight * 4;
     */
    public static native void longToByteArray(long var0, byte[] var2, int var3);

    /**
     * 功能：
     * 从内存地址获取RGBA数据并创建或复用Bitmap对象
     * 返回Bitmap对象（可能是新创建的，也可能是复用传入的）
     * 传入的Bitmap参数可以为null，此时会创建新的Bitmap
     * 实现细节：
     * 检查传入的Bitmap是否可以复用（尺寸和配置是否匹配）
     * 如果不能复用，则创建新的Bitmap对象
     * 使用Java层的setPixels方法设置像素数据，而不是直接操作内存
     * 同样执行RGBA到ARGB的格式转换和180度旋转
     * @param path
     * @param width
     * @param height
     * @param bitmap
     * @return
     */
    public static native Bitmap getBitmap(long path, int width, int height, Bitmap bitmap);

    /**
     * 直接使用字节数组检测图像中的人体姿态
     * 避免Bitmap创建和回收导致的内存错误
     * 图像数据会被旋转180度以修正Unity传递的倒立图像
     * @param imageData RGBA格式的图像字节数组
     * @param width 图像宽度
     * @param height 图像高度
     * @return 检测结果数组
     */
    public native PoseResult[] detectFromByteArray(byte[] imageData, int width, int height);
}
package com.cy.posedemo;


import android.content.Context;
import android.content.res.AssetManager;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class ModelUtils {
    private static final String TAG = "ModelUtils";

    /**
     * 从Assets复制模型文件到应用私有目录
     * @param context 上下文
     * @param assetName Assets中的文件名
     * @return 复制后的文件路径
     */
    public static String copyAssetToCache(Context context, String assetName) {
        File cacheFile = new File(context.getCacheDir(), assetName);
        // 如果文件已存在，直接返回路径
        if (cacheFile.exists()) {
            return cacheFile.getAbsolutePath();
        }
        try {
            AssetManager assetManager = context.getAssets();
            InputStream in = assetManager.open(assetName);
            OutputStream out = new FileOutputStream(cacheFile);
            byte[] buffer = new byte[1024];
            int read;
            while ((read = in.read(buffer)) != -1) {
                out.write(buffer, 0, read);
            }
            in.close();
            out.flush();
            out.close();
            Log.d(TAG, "Model copied to: " + cacheFile.getAbsolutePath());
            return cacheFile.getAbsolutePath();
        } catch (IOException e) {
            Log.e(TAG, "Failed to copy asset file: " + assetName, e);
            return null;
        }
    }
}

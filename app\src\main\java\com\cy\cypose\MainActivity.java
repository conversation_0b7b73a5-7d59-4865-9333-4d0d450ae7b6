package com.cy.cypose;

import androidx.appcompat.app.AppCompatActivity;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.os.Bundle;
import android.util.Log;
import android.widget.ImageView;
import android.widget.Toast;

import com.cykj.cymobile.ModelUtils;
import com.cykj.cymobile.PoseDetector;
import com.cykj.cymobile.PoseResult;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";

    private PoseDetector mPoseDetector;
    private ImageView mImageView;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        mImageView = findViewById(R.id.imageView);

        // 初始化检测器
        mPoseDetector = new PoseDetector();
        // 从Assets复制模型到缓存目录
        String modelPath = ModelUtils.copyAssetToCache(this, "yolov8n-pose.rknn");
        if (modelPath == null) {
            Toast.makeText(this, "模型文件复制失败", Toast.LENGTH_SHORT).show();
            return;
        }
        // 初始化模型
        int ret = mPoseDetector.init(modelPath);
        if (ret != 0) {
            Log.e(TAG, "Failed to initialize model: " + ret);
            Toast.makeText(this, "模型初始化失败", Toast.LENGTH_SHORT).show();
            return;
        }
        Bitmap bitmap = BitmapFactory.decodeResource(getResources(), R.drawable.img_four);
        PoseResult[] results = mPoseDetector.detect(bitmap);
        if (results != null && results.length > 0) {
            // 在图像上绘制检测结果
            Bitmap resultBitmap = drawResults(bitmap, results);
            mImageView.setImageBitmap(resultBitmap);
        } else {
            mImageView.setImageBitmap(bitmap);
        }
    }

    // 在图像上绘制检测结果
    private Bitmap drawResults(Bitmap bitmap, PoseResult[] results) {
        Bitmap resultBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true);
        Canvas canvas = new Canvas(resultBitmap);

        Paint boxPaint = new Paint();
        boxPaint.setColor(Color.GREEN);
        boxPaint.setStyle(Paint.Style.STROKE);
        boxPaint.setStrokeWidth(5);

        Paint pointPaint = new Paint();
        pointPaint.setColor(Color.RED);
        pointPaint.setStyle(Paint.Style.FILL);

        Paint linePaint = new Paint();
        linePaint.setColor(Color.YELLOW);
        linePaint.setStrokeWidth(3);

        // 人体骨架连接关系
        int[][] skeleton = {
                {5, 7}, {7, 9}, {6, 8}, {8, 10}, {5, 6}, {5, 11}, {6, 12}, {11, 12},
                {11, 13}, {13, 15}, {12, 14}, {14, 16}, {0, 1}, {0, 2}, {1, 3}, {2, 4}
        };

        for (PoseResult result : results) {
            // 绘制边界框
            canvas.drawRect(result.boxLeft, result.boxTop, result.boxRight, result.boxBottom, boxPaint);

            // 绘制关键点和骨架
            for (int i = 0; i < 17; i++) {
                float x = result.keypoints[i][0];
                float y = result.keypoints[i][1];
                float conf = result.keypoints[i][2];

                if (conf > 0.5) {
                    canvas.drawCircle(x, y, 5, pointPaint);
                }
            }

            // 绘制骨架连接
            for (int[] connection : skeleton) {
                int p1 = connection[0];
                int p2 = connection[1];

                float x1 = result.keypoints[p1][0];
                float y1 = result.keypoints[p1][1];
                float conf1 = result.keypoints[p1][2];

                float x2 = result.keypoints[p2][0];
                float y2 = result.keypoints[p2][1];
                float conf2 = result.keypoints[p2][2];

                if (conf1 > 0.5 && conf2 > 0.5) {
                    canvas.drawLine(x1, y1, x2, y2, linePaint);
                }
            }
        }

        return resultBitmap;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 释放资源
        if (mPoseDetector != null) {
            mPoseDetector.release();
        }
    }
}
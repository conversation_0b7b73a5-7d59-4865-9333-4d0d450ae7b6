other thread is trapped; signum = 11
2025-07-23 15:49:35.818  8178-8250  AndroidRuntime          com.cy.exercisetv                    E  FATAL EXCEPTION: UnityMain
                                                                                                    Process: com.cy.exercisetv, PID: 8178
                                                                                                    java.lang.Error: *** *** *** *** *** *** *** *** *** *** *** *** *** *** *** ***
                                                                                                    Version '2019.4.35f1 (0462406dff2e)', Build type 'Release', Scripting Backend 'il2cpp', CPU 'arm64-v8a'
                                                                                                    Build fingerprint: 'Geniatech/enjoytv/enjoytv:11/V011S003_20210511/20210511.100309:user/release-keys'
                                                                                                    Revision: '0'
                                                                                                    ABI: 'arm64'
                                                                                                    Timestamp: 2025-07-23 15:49:35+0800
                                                                                                    pid: 8178, tid: 8178, name: m.cy.exercisetv  >>> com.cy.exercisetv <<<
                                                                                                    uid: 10117
                                                                                                    signal 11 (SIGSEGV), code 2 (SEGV_ACCERR), fault addr 0x6e41ca3020
                                                                                                        x0  000000007740100c  x1  0000006e41ca3020  x2  000000000012c000  x3  0000000077401000
                                                                                                        x4  0000006e41dcf020  x5  000000007752d00c  x6  0000006e41ca3020  x7  000000006880940f
                                                                                                        x8  0000000077401000  x9  000000000000000c  x10 0000000070bf4e1c  x11 000000000000001e
                                                                                                        x12 0000000000000000  x13 0000007fea4254e8  x14 0000000000000001  x15 00000000ebad6a89
                                                                                                        x16 0000006e544de688  x17 00000070e664e180  x18 00000000000041a0  x19 000000000012c000
                                                                                                        x20 0000000000000000  x21 0000006e41ca3020  x22 b400006f14507090  x23 0000000077401000
                                                                                                        x24 0000007fea425350  x25 00000070e9c6c000  x26 b400006fc450ebe0  x27 00000070e9c6c000
                                                                                                        x28 0000006e544e5000  x29 0000007fea425390
                                                                                                        sp  0000007fea425320  lr  0000006e54297564  pc  00000070e664e0f8
                                                                                                    
                                                                                                    backtrace:
                                                                                                          #00 pc 000000000004a0f8  /apex/com.android.runtime/lib64/bionic/libc.so (__memcpy+232) (BuildId: 23b47e338fca2b85af1ec50fd590d7c7)
                                                                                                          #01 pc 0000000000451560  /apex/com.android.art/lib64/libart.so (art::JNI<true>::SetByteArrayRegion(_JNIEnv*, _jbyteArray*, int, int, signed char const*)+796) (BuildId: 56495344ae4c174f65a79d37faa0e963)
                                                                                                          #02 pc 000000000037fba0  /apex/com.android.art/lib64/libart.so (art::(anonymous namespace)::CheckJNI::SetPrimitiveArrayRegion(char const*, art::Primitive::Type, _JNIEnv*, _jarray*, int, int, void const*)+992) (BuildId: 56495344ae4c174f65a79d37faa0e963)
                                                                                                          #03 pc 000000000013ced4  /apex/com.android.art/lib64/libart.so (art_quick_generic_jni_trampoline+148) (BuildId: 56495344ae4c174f65a79d37faa0e963)
                                                                                                          #04 pc 00000000020240b4  /memfd:jit-cache (deleted)
                                                                                                    
                                                                                                    	at libc.__memcpy(__memcpy:232)
                                                                                                    	at libart.art::JNI<true>::SetByteArrayRegion(_JNIEnv*, _jbyteArray*, int, int, signed char const*)(SetByteArrayRegion:796)
                                                                                                    	at libart.art::(anonymous namespace)::CheckJNI::SetPrimitiveArrayRegion(char const*, art::Primitive::Type, _JNIEnv*, _jarray*, int, int, void const*)(:992)
                                                                                                    	at libart.art_quick_generic_jni_trampoline(art_quick_generic_jni_trampoline:148)
                                                                                                    	at memfd:jit-cache (deleted).0x20240b4(Native Method)









getWifiLinkLayerStats_1_3_Internal(l.973) failed {.code = ERROR_UNKNOWN, .description = unknown error}
2025-07-23 15:49:45.863  8178-8250  m.cy.exerciset          com.cy.exercisetv                    A  thread_list.cc:881] Thread suspension timed out: 0x6dd12d0ad8:main
2025-07-23 15:49:45.949  8178-8250  m.cy.exerciset          com.cy.exercisetv                    A  runtime.cc:655] Runtime aborting...
                                                                                                    runtime.cc:655] Skipping all-threads dump as locks are held:
                                                                                                    runtime.cc:655] Aborting thread:
                                                                                                    runtime.cc:655] "UnityMain" prio=5 tid=40 Runnable
                                                                                                    runtime.cc:655]   | group="" sCount=0 dsCount=0 flags=0 obj=0x130037e0 self=0xb400006fc455b3a0
                                                                                                    runtime.cc:655]   | sysTid=8250 nice=0 cgrp=default sched=0/0 handle=0x6dd12d2cc0
                                                                                                    runtime.cc:655]   | state=R schedstat=( 159073544725 11791559671 71110 ) utm=15584 stm=323 core=2 HZ=100
                                                                                                    runtime.cc:655]   | stack=0x6dd11cf000-0x6dd11d1000 stackSize=1043KB
                                                                                                    runtime.cc:655]   | held mutexes= "thread suspend count lock" "abort lock" "thread list lock" "mutator lock"(shared held)
                                                                                                    runtime.cc:655]   native: #00 pc 0000000000496638  /apex/com.android.art/lib64/libart.so (art::DumpNativeStack(std::__1::basic_ostream<char, std::__1::char_traits<char> >&, int, BacktraceMap*, char const*, art::ArtMethod*, void*, bool)+140)
                                                                                                    runtime.cc:655]   native: #01 pc 00000000005a01d8  /apex/com.android.art/lib64/libart.so (art::Thread::DumpStack(std::__1::basic_ostream<char, std::__1::char_traits<char> >&, bool, BacktraceMap*, bool) const+372)
                                                                                                    runtime.cc:655]   native: #02 pc 00000000005662a0  /apex/com.android.art/lib64/libart.so (art::AbortState::DumpThread(std::__1::basic_ostream<char, std::__1::char_traits<char> >&, art::Thread*) const+60)
                                                                                                    runtime.cc:655]   native: #03 pc 00000000005520c8  /apex/com.android.art/lib64/libart.so (art::Runtime::Abort(char const*)+2360)
                                                                                                    runtime.cc:655]   native: #04 pc 0000000000013990  /system/lib64/libbase.so (android::base::SetAborter(std::__1::function<void (char const*)>&&)::$_3::__invoke(char const*)+76)
                                                                                                    runtime.cc:655]   native: #05 pc 0000000000012fb4  /system/lib64/libbase.so (android::base::LogMessage::~LogMessage()+320)
                                                                                                    runtime.cc:655]   native: #06 pc 00000000005ba8b8  /apex/com.android.art/lib64/libart.so (art::ThreadSuspendByPeerWarning(art::Thread*, android::base::LogSeverity, char const*, _jobject*)+656)
                                                                                                    runtime.cc:655]   native: #07 pc 00000000005b9e50  /apex/com.android.art/lib64/libart.so (art::ThreadList::SuspendThreadByPeer(_jobject*, bool, art::SuspendReason, bool*)+1960)
                                                                                                    runtime.cc:655]   native: #08 pc 00000000004ac994  /apex/com.android.art/lib64/libart.so (art::VMStack_getThreadStackTrace(_JNIEnv*, _jclass*, _jobject*)+324)
                                                                                                    runtime.cc:655]   native: #09 pc 0000000000012e2c  /apex/com.android.art/javalib/arm64/boot-core-libart.oat (art_jni_trampoline+172)
                                                                                                    runtime.cc:655]   native: #10 pc 00000000001337e8  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+568)
                                                                                                    runtime.cc:655]   native: #11 pc 00000000001a8a88  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke(art::Thread*, unsigned int*, unsigned int, art::JValue*, char const*)+228)
                                                                                                    runtime.cc:655]   native: #12 pc 00000000003165ec  /apex/com.android.art/lib64/libart.so (art::interpreter::ArtInterpreterToCompiledCodeBridge(art::Thread*, art::ArtMethod*, art::ShadowFrame*, unsigned short, art::JValue*)+376)
                                                                                                    runtime.cc:655]   native: #13 pc 000000000030c6b0  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false, false>(art::ArtMethod*, art::Thread*, art::ShadowFrame&, art::Instruction const*, unsigned short, art::JValue*)+912)
                                                                                                    runtime.cc:655]   native: #14 pc 0000000000671ff8  /apex/com.android.art/lib64/libart.so (MterpInvokeStatic+536)
                                                                                                    runtime.cc:655]   native: #15 pc 000000000012d994  /apex/com.android.art/lib64/libart.so (mterp_op_invoke_static+20)
                                                                                                    runtime.cc:655]   native: #16 pc 0000000000003078  [anon:dalvik-/apex/com.android.art/javalib/core-oj.jar-transformed] (java.lang.Thread.getStackTrace)
                                                                                                    runtime.cc:655]   native: #17 pc 000000000066f36c  /apex/com.android.art/lib64/libart.so (MterpInvokeVirtual+1512)
                                                                                                    runtime.cc:655]   native: #18 pc 000000000012d814  /apex/com.android.art/lib64/libart.so (mterp_op_invoke_virtual+20)
                                                                                                    runtime.cc:655]   native: #19 pc 0000000000002b22  [anon:dalvik-/apex/com.android.art/javalib/core-oj.jar-transformed] (java.lang.Thread.getAllStackTraces+54)
2025-07-23 15:49:45.950  8178-8250  m.cy.exerciset          com.cy.exercisetv                    A  runtime.cc:655]   native: #20 pc 000000000067229c  /apex/com.android.art/lib64/libart.so (MterpInvokeStatic+1212)
                                                                                                    runtime.cc:655]   native: #21 pc 000000000012d994  /apex/com.android.art/lib64/libart.so (mterp_op_invoke_static+20)
                                                                                                    runtime.cc:655]   native: #22 pc 00000000001e182e  [anon:dalvik-classes.dex extracted in memory from /data/app/~~G8Ca76jRnx8QUf2dLRi6Cg==/com.cy.exercisetv-6dkbGwHCjoF1MDGz45izSQ==/base.apk] (com.umeng.crash.m.b+10)
                                                                                                    runtime.cc:655]   native: #23 pc 000000000067229c  /apex/com.android.art/lib64/libart.so (MterpInvokeStatic+1212)
                                                                                                    runtime.cc:655]   native: #24 pc 000000000012d994  /apex/com.android.art/lib64/libart.so (mterp_op_invoke_static+20)
                                                                                                    runtime.cc:655]   native: #25 pc 00000000001df0e0  [anon:dalvik-classes.dex extracted in memory from /data/app/~~G8Ca76jRnx8QUf2dLRi6Cg==/com.cy.exercisetv-6dkbGwHCjoF1MDGz45izSQ==/base.apk] (com.umeng.crash.d.uncaughtException+516)
                                                                                                    runtime.cc:655]   native: #26 pc 0000000000670d8c  /apex/com.android.art/lib64/libart.so (MterpInvokeInterface+1796)
                                                                                                    runtime.cc:655]   native: #27 pc 000000000012da14  /apex/com.android.art/lib64/libart.so (mterp_op_invoke_interface+20)
                                                                                                    runtime.cc:655]   native: #28 pc 00000000001f7b10  [anon:dalvik-classes.dex extracted in memory from /data/app/~~G8Ca76jRnx8QUf2dLRi6Cg==/com.cy.exercisetv-6dkbGwHCjoF1MDGz45izSQ==/base.apk] (com.unity3d.player.p.uncaughtException+196)
                                                                                                    runtime.cc:655]   native: #29 pc 0000000000670d8c  /apex/com.android.art/lib64/libart.so (MterpInvokeInterface+1796)
                                                                                                    runtime.cc:655]   native: #30 pc 000000000012da14  /apex/com.android.art/lib64/libart.so (mterp_op_invoke_interface+20)
                                                                                                    runtime.cc:655]   native: #31 pc 00000000000ea1f4  /apex/com.android.art/javalib/core-oj.jar (java.lang.ThreadGroup.uncaughtException+28)
                                                                                                    runtime.cc:655]   native: #32 pc 000000000066f36c  /apex/com.android.art/lib64/libart.so (MterpInvokeVirtual+1512)
                                                                                                    runtime.cc:655]   native: #33 pc 000000000012d814  /apex/com.android.art/lib64/libart.so (mterp_op_invoke_virtual+20)
                                                                                                    runtime.cc:655]   native: #34 pc 00000000000ea1e0  /apex/com.android.art/javalib/core-oj.jar (java.lang.ThreadGroup.uncaughtException+8)
                                                                                                    runtime.cc:655]   native: #35 pc 0000000000670d8c  /apex/com.android.art/lib64/libart.so (MterpInvokeInterface+1796)
                                                                                                    runtime.cc:655]   native: #36 pc 000000000012da14  /apex/com.android.art/lib64/libart.so (mterp_op_invoke_interface+20)
                                                                                                    runtime.cc:655]   native: #37 pc 0000000000002fee  [anon:dalvik-/apex/com.android.art/javalib/core-oj.jar-transformed] (java.lang.Thread.dispatchUncaughtException+38)
                                                                                                    runtime.cc:655]   native: #38 pc 0000000000303e34  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute(art::Thread*, art::CodeItemDataAccessor const&, art::ShadowFrame&, art::JValue, bool, bool) (.llvm.15654337495489426524)+268)
                                                                                                    runtime.cc:655]   native: #39 pc 000000000065de70  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+760)
                                                                                                    runtime.cc:655]   native: #40 pc 000000000013cff8  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88)
                                                                                                    runtime.cc:655]   native: #41 pc 0000000000133564  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+548)
                                                                                                    runtime.cc:655]   native: #42 pc 00000000001a8a6c  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke(art::Thread*, unsigned int*, unsigned int, art::JValue*, char const*)+200)
                                                                                                    runtime.cc:655]   native: #43 pc 000000000054a0a8  /apex/com.android.art/lib64/libart.so (art::JValue art::InvokeVirtualOrInterfaceWithVarArgs<art::ArtMethod*>(art::ScopedObjectAccessAlreadyRunnable const&, _jobject*, art::ArtMethod*, std::__va_list)+468)
                                                                                                    runtime.cc:655]   native: #44 pc 000000000054a248  /apex/com.android.art/lib64/libart.so (art::JValue art::InvokeVirtualOrInterfaceWithVarArgs<_jmethodID*>(art::ScopedObjectAccessAlreadyRunnable const&, _jobject*, _jmethodID*, std::__va_list)+92)
                                                                                                    runtime.cc:655]   native: #45 pc 000000000040e650  /apex/com.android.art/lib64/libart.so (art::JNI<true>::CallVoidMethodV(_JNIEnv*, _jobject*, _jmethodID*, std::__va_list)+644)
2025-07-23 15:49:45.950  8178-8250  m.cy.exerciset          com.cy.exercisetv                    A  runtime.cc:655]   native: #46 pc 0000000000376b70  /apex/com.android.art/lib64/libart.so (art::(anonymous namespace)::CheckJNI::CallMethodV(char const*, _JNIEnv*, _jobject*, _jclass*, _jmethodID*, std::__va_list, art::Primitive::Type, art::InvokeType)+2512)
                                                                                                    runtime.cc:655]   native: #47 pc 0000000000364f4c  /apex/com.android.art/lib64/libart.so (art::(anonymous namespace)::CheckJNI::CallVoidMethodV(_JNIEnv*, _jobject*, _jmethodID*, std::__va_list)+72)
                                                                                                    runtime.cc:655]   native: #48 pc 00000000002eb9c0  /apex/com.android.art/lib64/libart.so (_JNIEnv::CallVoidMethod(_jobject*, _jmethodID*, ...)+124)
                                                                                                    runtime.cc:655]   native: #49 pc 00000000005a8534  /apex/com.android.art/lib64/libart.so (art::Thread::HandleUncaughtExceptions(art::ScopedObjectAccessAlreadyRunnable&)+556)
                                                                                                    runtime.cc:655]   native: #50 pc 00000000005a742c  /apex/com.android.art/lib64/libart.so (art::Thread::Destroy()+1256)
                                                                                                    runtime.cc:655]   native: #51 pc 00000000005bbdfc  /apex/com.android.art/lib64/libart.so (art::ThreadList::Unregister(art::Thread*)+144)
                                                                                                    runtime.cc:655]   native: #52 pc 00000000005984d0  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback(void*)+1344)
                                                                                                    runtime.cc:655]   native: #53 pc 00000000000af880  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start(void*)+64)
                                                                                                    runtime.cc:655]   native: #54 pc 00000000000500d0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64)
                                                                                                    runtime.cc:655]   at dalvik.system.VMStack.getThreadStackTrace(Native method)
                                                                                                    runtime.cc:655]   at java.lang.Thread.getStackTrace(Thread.java:1736)
                                                                                                    runtime.cc:655]   at java.lang.Thread.getAllStackTraces(Thread.java:1812)
                                                                                                    runtime.cc:655]   at com.umeng.crash.m.b(UCrash:870)
                                                                                                    runtime.cc:655]   at com.umeng.crash.d.uncaughtException(UCrash:1126)
                                                                                                    runtime.cc:655]   at com.unity3d.player.p.uncaughtException(unavailable:-1)
                                                                                                    runtime.cc:655]   - locked <0x0730630f> (a com.unity3d.player.p)
                                                                                                    runtime.cc:655]   at java.lang.ThreadGroup.uncaughtException(ThreadGroup.java:1073)
                                                                                                    runtime.cc:655]   at java.lang.ThreadGroup.uncaughtException(ThreadGroup.java:1068)
                                                                                                    runtime.cc:655]   at java.lang.Thread.dispatchUncaughtException(Thread.java:2203)
                                                                                                    runtime.cc:655] 
2025-07-23 15:49:45.950  8178-8250  CRASH                   com.cy.exercisetv                    E  Forwarding signal 6
cmake_minimum_required(VERSION 3.10)

project(rknn_pose_detector)

# 添加定义
add_definitions(-DRKNPU2)

# 设置RKNN库路径 - 确保这个路径是正确的
set(RKNN_LIB_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI})
set(RKNN_INCLUDE_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/include)

# 添加RKNN库 - 使用INTERFACE而不是SHARED，避免复制.so文件
add_library(rknnrt INTERFACE)
# 注意：INTERFACE库不使用IMPORTED_LOCATION属性
target_include_directories(rknnrt INTERFACE ${RKNN_INCLUDE_PATH})
target_link_libraries(rknnrt INTERFACE ${RKNN_LIB_PATH}/librknnrt.so)

# 创建共享库
add_library(${PROJECT_NAME} SHARED
        postprocess.cc
        rknpu2/yolov8-pose.cc
        pose_detector_jni.cc
        image_utils.c
)

# 包含头文件目录
target_include_directories(${PROJECT_NAME} PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_CURRENT_SOURCE_DIR}/rknpu2
        ${RKNN_INCLUDE_PATH}
)

# 设置RGA库路径
set(RGA_LIB_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI})

# 添加动态库 librga.so - 使用INTERFACE而不是SHARED，避免复制.so文件
add_library(rga_shared INTERFACE)
target_link_libraries(rga_shared INTERFACE ${RGA_LIB_PATH}/librga.so)

# 不再使用静态库 librga.a，只使用动态库 librga.so
# add_library(rga_static STATIC IMPORTED)
# set_target_properties(rga_static PROPERTIES IMPORTED_LOCATION ${RGA_LIB_PATH}/librga.a)

# 添加链接选项，确保正确链接标准库
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,--no-as-needed")

# 链接库 - 只使用动态库版本的 RGA
target_link_libraries(${PROJECT_NAME}
        # 标准库
        -lc
        -lc++
        -lm
        -ldl
        -latomic
        # Android 库
        log
        jnigraphics
        android
        # 项目特定的库
        rga_shared
        rknnrt
)

# 启用详细的构建输出
set(CMAKE_VERBOSE_MAKEFILE ON)
